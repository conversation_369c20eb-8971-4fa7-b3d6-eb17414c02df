// 简单测试智能patch修复功能
public class TestSmartPatch {
    public static void main(String[] args) {
        // 模拟原始代码
        String originalCode = "package com.pulse_oyo_test_a.drug_inventory.entrance.web.converter;\n\n" +
                "import java.util.*;\n\n" +
                "@Component\n" +
                "public class PharmacyInboundSaveResultVoConverter {\n\n" +
                "    public Map<DrugImportBaseDto, PharmacyInboundSaveResultVo> convertToPharmacyInboundSaveResultVoMap(List<DrugImportBaseDto> dtoList) {\n" +
                "        if (CollectionUtil.isEmpty(dtoList)) {\n" +
                "            return new HashMap<>();\n" +
                "        }\n\n" +
                "        Map<DrugImportBaseDto, PharmacyInboundSaveResultVo> voMap = dtoList\n" +
                "            .stream()\n" +
                "            .filter(Objects::nonNull)\n" +
                "            .collect(\n" +
                "                Collectors.toMap(\n" +
                "                    Function.identity(),\n" +
                "                    dto -> {\n" +
                "                        PharmacyInboundSaveResultVo vo = new PharmacyInboundSaveResultVo();\n" +
                "                        vo.setId(dto.getId());\n" +
                "                        return vo;\n" +
                "                    },\n" +
                "                    (o1, o2) -> o1,\n" +
                "                    LinkedHashMap::new\n" +
                "                )\n" +
                "            );\n" +
                "        /** This block is generated by vs, do not modify, end anchor 1 */\n" +
                "        //自定义处理逻辑\n" +
                "        /** This block is generated by vs, do not modify, start anchor 2 */\n" +
                "        return voMap;\n" +
                "        /** This block is generated by vs, do not modify, end anchor 2 */\n" +
                "    }\n" +
                "}";

        // 模拟有行号偏移问题的patch
        String problematicPatch = "@@ -78,7 +78,15 @@\n" +
                "         )\n" +
                "      );\n" +
                "    /** This block is generated by vs, do not modify, end anchor 1 */\n" +
                "    //自定义处理逻辑\n" +
                "+    // 设置自定义字段值\n" +
                "+    for (Map.Entry<DrugImportBaseDto, PharmacyInboundSaveResultVo> entry : voMap.entrySet()) {\n" +
                "+      PharmacyInboundSaveResultVo vo = entry.getValue();\n" +
                "+      // 设置默认值，实际使用时应根据具体业务逻辑设置\n" +
                "+      vo.setSuccess(true);\n" +
                "+      vo.setMessage(\"操作成功\");\n" +
                "+    }\n" +
                "+\n" +
                "    /** This block is generated by vs, do not modify, start anchor 2 */\n" +
                "    return voMap;\n" +
                "    /** This block is generated by vs, do not modify, end anchor 2 */";

        System.out.println("=== 测试智能patch修复功能 ===");
        System.out.println("原始代码长度: " + originalCode.length());
        System.out.println("Patch内容: " + problematicPatch);
        
        // 这里应该调用 TextFilePatcher.patchText(originalCode, problematicPatch)
        // 但由于我们在Java环境中，无法直接调用Kotlin代码
        // 实际测试需要在IntelliJ插件环境中进行
        
        System.out.println("测试需要在IntelliJ插件环境中运行");
    }
}
