package com.think1024.tocodesign.ideaplugin.toco

/**
 * 手动测试智能patch修复功能
 * 运行这个main函数来测试智能修复是否工作
 */
fun main() {
    testSmartPatchRepairWithLineNumberOffset()
    testSmartPatchRepairFallbackToStandardWhenNoIssue()
    println("All tests completed!")
}

fun testSmartPatchRepairWithLineNumberOffset() {
        // 模拟原始代码（简化版本）
        val originalCode = """
package com.pulse_oyo_test_a.drug_inventory.entrance.web.converter;

import java.util.*;

@Component
public class PharmacyInboundSaveResultVoConverter {

    public Map<DrugImportBaseDto, PharmacyInboundSaveResultVo> convertToPharmacyInboundSaveResultVoMap(List<DrugImportBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugImportBaseDto, PharmacyInboundSaveResultVo> voMap = dtoList
            .stream()
            .filter(Objects::nonNull)
            .collect(
                Collectors.toMap(
                    Function.identity(),
                    dto -> {
                        PharmacyInboundSaveResultVo vo = new PharmacyInboundSaveResultVo();
                        vo.setId(dto.getId());
                        return vo;
                    },
                    (o1, o2) -> o1,
                    LinkedHashMap::new
                )
            );
        /** This block is generated by vs, do not modify, end anchor 1 */
        //自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
        """.trimIndent()

        // 模拟有行号偏移问题的patch
        val problematicPatch = """
@@ -78,7 +78,15 @@
         )
      );
    /** This block is generated by vs, do not modify, end anchor 1 */
    //自定义处理逻辑
+    // 设置自定义字段值
+    for (Map.Entry<DrugImportBaseDto, PharmacyInboundSaveResultVo> entry : voMap.entrySet()) {
+      PharmacyInboundSaveResultVo vo = entry.getValue();
+      // 设置默认值，实际使用时应根据具体业务逻辑设置
+      vo.setSuccess(true);
+      vo.setMessage("操作成功");
+    }
+
    /** This block is generated by vs, do not modify, start anchor 2 */
    return voMap;
    /** This block is generated by vs, do not modify, end anchor 2 */
        """.trimIndent()

        // 测试智能修复
        val result = TextFilePatcher.patchText(originalCode, problematicPatch)
        
        // 验证结果
        if (result.first == null) {
            println("❌ Smart patch repair failed: ${result.second}")
            return
        }

        val patchedCode = result.first!!

        // 验证新增的代码是否正确插入
        val hasAddedComment = patchedCode.contains("// 设置自定义字段值")
        val hasSuccessSetting = patchedCode.contains("vo.setSuccess(true);")
        val hasMessageSetting = patchedCode.contains("vo.setMessage(\"操作成功\");")

        if (!hasAddedComment || !hasSuccessSetting || !hasMessageSetting) {
            println("❌ Added code not found in patched result")
            println("Has comment: $hasAddedComment")
            println("Has success: $hasSuccessSetting")
            println("Has message: $hasMessageSetting")
            return
        }

        // 验证插入位置是否正确（在自定义处理逻辑注释之后）
        val lines = patchedCode.lines()
        val customCommentIndex = lines.indexOfFirst { it.trim().contains("//自定义处理逻辑") }
        val addedCommentIndex = lines.indexOfFirst { it.trim().contains("// 设置自定义字段值") }

        if (customCommentIndex < 0) {
            println("❌ Could not find custom logic comment")
            return
        }

        if (addedCommentIndex <= customCommentIndex) {
            println("❌ Added code not in correct position")
            println("Custom comment at line: $customCommentIndex")
            println("Added comment at line: $addedCommentIndex")
            return
        }

        println("✅ Smart patch repair test passed!")
        println("📝 Patched code preview:")
        val lines = patchedCode.lines()
        val startIndex = maxOf(0, customCommentIndex - 2)
        val endIndex = minOf(lines.size, addedCommentIndex + 8)
        for (i in startIndex until endIndex) {
            val marker = if (i == customCommentIndex) "👉 " else if (i >= addedCommentIndex && i < addedCommentIndex + 5) "✨ " else "   "
            println("$marker${i + 1}: ${lines[i]}")
        }
    }

fun testSmartPatchRepairFallbackToStandardWhenNoIssue() {
        // 测试当patch没有问题时，应该使用标准方法
        val originalCode = """
line 1
line 2
line 3
        """.trimIndent()

        val validPatch = """
@@ -1,3 +1,4 @@
 line 1
+new line
 line 2
 line 3
        """.trimIndent()

        val result = TextFilePatcher.patchText(originalCode, validPatch)

        if (result.first == null) {
            println("❌ Standard patch failed: ${result.second}")
            return
        }

        val patchedCode = result.first!!
        if (!patchedCode.contains("new line")) {
            println("❌ Standard patch did not add expected line")
            return
        }

        println("✅ Standard patch fallback test passed!")
    }
}
