package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diff.impl.patch.ApplyPatchStatus
import com.intellij.openapi.diff.impl.patch.PatchReader
import com.intellij.openapi.diff.impl.patch.apply.GenericPatchApplier

object TextFilePatcher {
    private val logger = Logger.getInstance(TextFilePatcher::class.java)

    fun patchText(origText: String, patchText: String): Pair<String?, String?> {
        try {
            // 校验patch与原始文件的一致性
            val validationError = validatePatchConsistency(origText, patchText)
            if (validationError != null) {
                logger.warn("patch validation failed: $validationError\npatch:\n$patchText\norig:\n$origText")
                return Pair(null, validationError)
            }

            // @@里的新增代码行数和实际不一致，需要检查并修复这个计数
            val fixedPatch = fixPatchLineNumbers(patchText)

            // 检查并处理patch格式，移除前两行如果包含---和+++
            val processedPatch = preprocessPatch(fixedPatch)

            val gitDiffPath = "diff --git a/test b/test\n" +
                    "--- a/test\n" +
                    "+++ b/test\n" + processedPatch
            val patchReader = PatchReader(gitDiffPath)
            val patches = patchReader.readTextPatches()
            val patch = patches.firstOrNull()
            if (patch != null) {
                val appliedPatch = GenericPatchApplier.apply(origText, patch.hunks)
                if (appliedPatch?.status == ApplyPatchStatus.SUCCESS) {
                    return Pair(appliedPatch.patchedText, null)
                } else {
                    // 尝试智能修复patch并重新应用
                    logger.info("Standard patch application failed, attempting smart patch repair...")
                    val smartRepairResult = attemptSmartPatchRepair(origText, processedPatch)
                    if (smartRepairResult.first != null) {
                        logger.info("Smart patch repair succeeded")
                        return smartRepairResult
                    }

                    // 获取patch错误信息
                    try {
                        val patcher = PatchApplier(origText, patch.hunks)
                        patcher.execute()
                        logger.warn("patch apply failed\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, "patch apply failed")
                    } catch (e: Exception) {
                        logger.warn("patch apply failed: ${e.message}\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, e.message)
                    }
                }
            } else {
                logger.warn("patch parse failed\npatch:\n$processedPatch\norig:\n $origText")
                return Pair(null, "patch parse failed")
            }
        } catch (e: Exception) {
            logger.warn("fail to patch text: ${e.message}")
            return Pair(null, e.message)
        }
    }

    private fun preprocessPatch(patch: String): String {
        val lines = patch.lines()
        if (lines.size >= 2) {
            val firstLine = lines[0].trim()
            val secondLine = lines[1].trim()

            // 如果前两行包含---和+++，则移除它们
            if (firstLine.startsWith("---") && secondLine.startsWith("+++")) {
                return lines.drop(2).joinToString("\n")
            }
        }
        return patch
    }

    private fun fixPatchLineNumbers(patch: String): String {
        val lines = patch.lines().toMutableList()
        var i = 0
        while (i < lines.size) {
            val line = lines[i]
            if (line.startsWith("@@")) {
                // 解析hunk头部
                val hunkMatch = Regex("@@ -([0-9]+),([0-9]+) \\+([0-9]+),([0-9]+) @@").find(line)
                if (hunkMatch != null) {
                    val (oldStart, oldCount, newStart, newCount) = hunkMatch.destructured

                    // 计算实际的行数
                    var actualOldCount = 0  // 原始文件相关行数
                    var actualNewCount = 0  // 新文件相关行数
                    var j = i + 1
                    while (j < lines.size && !lines[j].startsWith("@@")) {
                        val hunkLine = lines[j]
                        when {
                            hunkLine.startsWith("+") -> {
                                // 新增行：只在新文件中存在
                                actualNewCount++
                            }
                            hunkLine.startsWith("-") -> {
                                // 删除行：只在原始文件中存在
                                actualOldCount++
                            }
                            hunkLine.startsWith(" ") || (!hunkLine.startsWith("\\")) -> {
                                // 上下文行：在新旧文件中都存在
                                // 注意：以\开头的行（如\ No newline at end of file）不计入行数
                                if (!hunkLine.startsWith("\\")) {
                                    actualOldCount++
                                    actualNewCount++
                                }
                            }
                        }
                        j++
                    }

                    // 如果实际行数与声明的不一致，修复它
                    val needsFix = actualOldCount.toString() != oldCount || actualNewCount.toString() != newCount
                    if (needsFix) {
                        lines[i] = "@@ -$oldStart,$actualOldCount +$newStart,$actualNewCount @@"
                        logger.warn("Fixed hunk header: original was '$line', fixed to '${lines[i]}'")
                    }
                }
            }
            i++
        }
        return lines.joinToString("\n")
    }

    private fun isNewFilePatch(patch: String): Boolean {
        val lines = patch.lines()
        
        // 检查是否包含 /dev/null 标识（新文件的典型标志）
        val hasDevNull = lines.any { it.trim().startsWith("---") && it.contains("/dev/null") }
        
        // 检查@@行是否以-0,0开头（表示源文件为空）
        val hasZeroStart = lines.any { line ->
            line.startsWith("@@") && Regex("@@ -0,0 \\+").containsMatchIn(line)
        }
        
        return hasDevNull || hasZeroStart
    }

    private fun validatePatchConsistency(origText: String, patchText: String): String? {
        val lines = patchText.lines()
        val origLines = origText.lines()
        val origLineCount = if (origText.isEmpty()) 0 else origLines.size
        
        for (line in lines) {
            if (line.startsWith("@@")) {
                val hunkMatch = Regex("@@ -([0-9]+),([0-9]+) \\+([0-9]+),([0-9]+) @@").find(line)
                if (hunkMatch != null) {
                    val (oldStart, oldCount, newStart, newCount) = hunkMatch.destructured
                    val oldStartInt = oldStart.toInt()
                    val oldCountInt = oldCount.toInt()
                    
                    // 检查-n,0的情况：表示从第n行开始，删除0行（即原文件为空）
                    if (oldCountInt == 0) {
                        if (origLineCount > 0) {
                            return "patch expects empty file but original file has $origLineCount lines, please check the patch code"
                        }
                    }
                    
                    // 检查其他不一致的情况
                    if (oldStartInt > 0 && oldStartInt + oldCountInt - 1 > origLineCount) {
                        return "patch references lines beyond original file length (${oldStartInt + oldCountInt - 1} > $origLineCount)"
                    }
                }
            }
        }
        
        return null
    }

    /**
     * 智能修复patch，当标准应用失败时尝试通过上下文匹配来修复行号偏移
     * 专门处理行号偏移导致的patch应用失败问题
     */
    private fun attemptSmartPatchRepair(origText: String, patchText: String): Pair<String?, String?> {
        try {
            logger.info("Starting smart patch repair...")

            // 解析patch内容
            val patchInfo = parsePatchContent(patchText) ?: return Pair(null, "Failed to parse patch content")

            // 查找最佳插入位置
            val bestPosition = findBestInsertPosition(origText, patchInfo)
            if (bestPosition == null) {
                return Pair(null, "Could not find suitable insertion position")
            }

            logger.info("Found best insertion position: line ${bestPosition.lineNumber}, anchor: '${bestPosition.anchorLine}'")

            // 应用patch到找到的位置
            return applyPatchAtPosition(origText, patchInfo, bestPosition)

        } catch (e: Exception) {
            logger.warn("Smart patch repair failed: ${e.message}")
            return Pair(null, "Smart repair error: ${e.message}")
        }
    }

    /**
     * 解析patch内容，提取关键信息
     */
    private fun parsePatchContent(patchText: String): PatchInfo? {
        val lines = patchText.lines()
        val hunkIndex = lines.indexOfFirst { it.startsWith("@@") }
        if (hunkIndex == -1) return null

        val contextLines = mutableListOf<String>()
        val addedLines = mutableListOf<String>()
        var foundComment = false

        // 从hunk后开始解析
        for (i in (hunkIndex + 1) until lines.size) {
            val line = lines[i]
            when {
                line.startsWith(" ") -> {
                    contextLines.add(line.substring(1))
                }
                line.startsWith("+") -> {
                    addedLines.add(line.substring(1))
                }
                line.startsWith("-") -> {
                    // 忽略删除行，我们主要关注插入
                }
                line.startsWith("\\") -> continue
                line.trim().startsWith("//") && line.contains("自定义处理逻辑") -> {
                    foundComment = true
                    break
                }
                else -> break
            }
        }

        return PatchInfo(contextLines, addedLines, foundComment)
    }

    /**
     * 查找最佳插入位置
     */
    private fun findBestInsertPosition(origText: String, patchInfo: PatchInfo): InsertPosition? {
        val origLines = origText.lines()
        logger.info("Searching for insertion position in ${origLines.size} lines")

        // 查找特定的锚点：注释行 "//自定义处理逻辑"
        for (i in origLines.indices) {
            val line = origLines[i].trim()
            if (line.startsWith("//") && line.contains("自定义处理逻辑")) {
                logger.info("Found custom logic comment at line ${i + 1}: '$line'")
                return InsertPosition(i + 1, line) // 在注释后插入
            }
        }

        // 如果没找到注释，查找其他可能的锚点
        // 查找 "/** This block is generated by vs, do not modify, end anchor 1 */"
        for (i in origLines.indices) {
            val line = origLines[i].trim()
            if (line.contains("end anchor 1")) {
                logger.info("Found end anchor 1 at line ${i + 1}")
                // 在这行后面查找注释行
                for (j in (i + 1) until origLines.size) {
                    val nextLine = origLines[j].trim()
                    if (nextLine.startsWith("//") && nextLine.contains("自定义")) {
                        logger.info("Found custom comment after anchor 1 at line ${j + 1}: '$nextLine'")
                        return InsertPosition(j + 1, nextLine)
                    }
                    if (nextLine.contains("start anchor 2")) {
                        logger.info("Found start anchor 2 at line ${j + 1}, inserting before it")
                        // 在start anchor 2之前插入
                        return InsertPosition(j, "before anchor 2")
                    }
                }
            }
        }

        logger.warn("Could not find suitable insertion position")
        return null
    }

    /**
     * 在指定位置应用patch
     */
    private fun applyPatchAtPosition(origText: String, patchInfo: PatchInfo, position: InsertPosition): Pair<String?, String?> {
        val origLines = origText.lines().toMutableList()

        // 在指定位置插入新代码
        var insertIndex = position.lineNumber
        for (addedLine in patchInfo.addedLines) {
            origLines.add(insertIndex, addedLine)
            insertIndex++
        }

        val result = origLines.joinToString("\n")
        return Pair(result, null)
    }

    /**
     * Patch信息数据类
     */
    private data class PatchInfo(
        val contextLines: List<String>,
        val addedLines: List<String>,
        val hasCustomComment: Boolean
    )

    /**
     * 插入位置数据类
     */
    private data class InsertPosition(
        val lineNumber: Int,
        val anchorLine: String
    )
}
