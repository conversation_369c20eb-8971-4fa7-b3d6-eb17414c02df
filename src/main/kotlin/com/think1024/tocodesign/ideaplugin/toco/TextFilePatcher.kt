package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diff.impl.patch.ApplyPatchStatus
import com.intellij.openapi.diff.impl.patch.PatchReader
import com.intellij.openapi.diff.impl.patch.apply.GenericPatchApplier

object TextFilePatcher {
    private val logger = Logger.getInstance(TextFilePatcher::class.java)

    fun patchText(origText: String, patchText: String): Pair<String?, String?> {
        try {
            // 校验patch与原始文件的一致性
            val validationError = validatePatchConsistency(origText, patchText)
            if (validationError != null) {
                logger.warn("patch validation failed: $validationError\npatch:\n$patchText\norig:\n$origText")
                return Pair(null, validationError)
            }

            // @@里的新增代码行数和实际不一致，需要检查并修复这个计数
            val fixedPatch = fixPatchLineNumbers(patchText)

            // 检查并处理patch格式，移除前两行如果包含---和+++
            val processedPatch = preprocessPatch(fixedPatch)

            val gitDiffPath = "diff --git a/test b/test\n" +
                    "--- a/test\n" +
                    "+++ b/test\n" + processedPatch
            val patchReader = PatchReader(gitDiffPath)
            val patches = patchReader.readTextPatches()
            val patch = patches.firstOrNull()
            if (patch != null) {
                val appliedPatch = GenericPatchApplier.apply(origText, patch.hunks)
                if (appliedPatch?.status == ApplyPatchStatus.SUCCESS) {
                    return Pair(appliedPatch.patchedText, null)
                } else {
                    // 尝试智能修复patch并重新应用
                    logger.info("Standard patch application failed, attempting smart patch repair...")
                    val smartRepairResult = attemptSmartPatchRepair(origText, processedPatch)
                    if (smartRepairResult.first != null) {
                        logger.info("Smart patch repair succeeded")
                        return smartRepairResult
                    }

                    // 获取patch错误信息
                    try {
                        val patcher = PatchApplier(origText, patch.hunks)
                        patcher.execute()
                        logger.warn("patch apply failed\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, "patch apply failed")
                    } catch (e: Exception) {
                        logger.warn("patch apply failed: ${e.message}\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, e.message)
                    }
                }
            } else {
                logger.warn("patch parse failed\npatch:\n$processedPatch\norig:\n $origText")
                return Pair(null, "patch parse failed")
            }
        } catch (e: Exception) {
            logger.warn("fail to patch text: ${e.message}")
            return Pair(null, e.message)
        }
    }

    private fun preprocessPatch(patch: String): String {
        val lines = patch.lines()
        if (lines.size >= 2) {
            val firstLine = lines[0].trim()
            val secondLine = lines[1].trim()

            // 如果前两行包含---和+++，则移除它们
            if (firstLine.startsWith("---") && secondLine.startsWith("+++")) {
                return lines.drop(2).joinToString("\n")
            }
        }
        return patch
    }

    private fun fixPatchLineNumbers(patch: String): String {
        val lines = patch.lines().toMutableList()
        var i = 0
        while (i < lines.size) {
            val line = lines[i]
            if (line.startsWith("@@")) {
                // 解析hunk头部
                val hunkMatch = Regex("@@ -([0-9]+),([0-9]+) \\+([0-9]+),([0-9]+) @@").find(line)
                if (hunkMatch != null) {
                    val (oldStart, oldCount, newStart, newCount) = hunkMatch.destructured

                    // 计算实际的行数
                    var actualOldCount = 0  // 原始文件相关行数
                    var actualNewCount = 0  // 新文件相关行数
                    var j = i + 1
                    while (j < lines.size && !lines[j].startsWith("@@")) {
                        val hunkLine = lines[j]
                        when {
                            hunkLine.startsWith("+") -> {
                                // 新增行：只在新文件中存在
                                actualNewCount++
                            }
                            hunkLine.startsWith("-") -> {
                                // 删除行：只在原始文件中存在
                                actualOldCount++
                            }
                            hunkLine.startsWith(" ") || (!hunkLine.startsWith("\\")) -> {
                                // 上下文行：在新旧文件中都存在
                                // 注意：以\开头的行（如\ No newline at end of file）不计入行数
                                if (!hunkLine.startsWith("\\")) {
                                    actualOldCount++
                                    actualNewCount++
                                }
                            }
                        }
                        j++
                    }

                    // 如果实际行数与声明的不一致，修复它
                    val needsFix = actualOldCount.toString() != oldCount || actualNewCount.toString() != newCount
                    if (needsFix) {
                        lines[i] = "@@ -$oldStart,$actualOldCount +$newStart,$actualNewCount @@"
                        logger.warn("Fixed hunk header: original was '$line', fixed to '${lines[i]}'")
                    }
                }
            }
            i++
        }
        return lines.joinToString("\n")
    }

    private fun isNewFilePatch(patch: String): Boolean {
        val lines = patch.lines()
        
        // 检查是否包含 /dev/null 标识（新文件的典型标志）
        val hasDevNull = lines.any { it.trim().startsWith("---") && it.contains("/dev/null") }
        
        // 检查@@行是否以-0,0开头（表示源文件为空）
        val hasZeroStart = lines.any { line ->
            line.startsWith("@@") && Regex("@@ -0,0 \\+").containsMatchIn(line)
        }
        
        return hasDevNull || hasZeroStart
    }

    private fun validatePatchConsistency(origText: String, patchText: String): String? {
        val lines = patchText.lines()
        val origLines = origText.lines()
        val origLineCount = if (origText.isEmpty()) 0 else origLines.size
        
        for (line in lines) {
            if (line.startsWith("@@")) {
                val hunkMatch = Regex("@@ -([0-9]+),([0-9]+) \\+([0-9]+),([0-9]+) @@").find(line)
                if (hunkMatch != null) {
                    val (oldStart, oldCount, newStart, newCount) = hunkMatch.destructured
                    val oldStartInt = oldStart.toInt()
                    val oldCountInt = oldCount.toInt()
                    
                    // 检查-n,0的情况：表示从第n行开始，删除0行（即原文件为空）
                    if (oldCountInt == 0) {
                        if (origLineCount > 0) {
                            return "patch expects empty file but original file has $origLineCount lines, please check the patch code"
                        }
                    }
                    
                    // 检查其他不一致的情况
                    if (oldStartInt > 0 && oldStartInt + oldCountInt - 1 > origLineCount) {
                        return "patch references lines beyond original file length (${oldStartInt + oldCountInt - 1} > $origLineCount)"
                    }
                }
            }
        }
        
        return null
    }

    /**
     * 智能修复patch，当标准应用失败时尝试通过上下文匹配来修复行号偏移
     * 专门处理行号偏移导致的patch应用失败问题
     */
    private fun attemptSmartPatchRepair(origText: String, patchText: String): Pair<String?, String?> {
        try {
            logger.info("Starting smart patch repair...")

            // 解析patch内容
            val patchInfo = parsePatchContent(patchText) ?: return Pair(null, "Failed to parse patch content")

            // 查找最佳插入位置
            val bestPosition = findBestInsertPosition(origText, patchInfo)
            if (bestPosition == null) {
                return Pair(null, "Could not find suitable insertion position")
            }

            logger.info("Found best insertion position: line ${bestPosition.lineNumber}, anchor: '${bestPosition.anchorLine}'")

            // 重新构建patch，修正hunk header，然后走标准apply流程
            return rebuildAndApplyPatch(origText, patchText, patchInfo, bestPosition)

        } catch (e: Exception) {
            logger.warn("Smart patch repair failed: ${e.message}")
            return Pair(null, "Smart repair error: ${e.message}")
        }
    }

    /**
     * 解析patch内容，提取关键信息
     */
    private fun parsePatchContent(patchText: String): PatchInfo? {
        val lines = patchText.lines()
        val hunkIndex = lines.indexOfFirst { it.startsWith("@@") }
        if (hunkIndex == -1) return null

        val contextLines = mutableListOf<String>()
        val addedLines = mutableListOf<String>()
        var foundComment = false
        var inAddedSection = false

        // 从hunk后开始解析
        for (i in (hunkIndex + 1) until lines.size) {
            val line = lines[i]
            when {
                line.startsWith(" ") -> {
                    // 上下文行
                    if (!inAddedSection) {
                        contextLines.add(line.substring(1))
                    }
                    // 如果已经开始添加行，上下文行就不再作为前置上下文
                }
                line.startsWith("+") -> {
                    inAddedSection = true
                    addedLines.add(line.substring(1))
                }
                line.startsWith("-") -> {
                    // 删除行通常在上下文中，但我们主要关注插入
                    if (!inAddedSection) {
                        // 删除行也算作上下文的一部分，但我们不包含它们在重建的patch中
                    }
                }
                line.startsWith("\\") -> continue // 忽略"\ No newline at end of file"等
                line.trim().startsWith("//") && line.contains("自定义处理逻辑") -> {
                    foundComment = true
                    if (!inAddedSection) {
                        contextLines.add(line.trim())
                    }
                    break
                }
                else -> break // 遇到下一个hunk或结束
            }
        }

        logger.info("Parsed patch: ${contextLines.size} context lines, ${addedLines.size} added lines")
        return PatchInfo(contextLines, addedLines, foundComment)
    }

    /**
     * 查找最佳插入位置
     */
    private fun findBestInsertPosition(origText: String, patchInfo: PatchInfo): InsertPosition? {
        val origLines = origText.lines()
        logger.info("Searching for insertion position in ${origLines.size} lines")

        // 查找特定的锚点：注释行 "//自定义处理逻辑"
        for (i in origLines.indices) {
            val line = origLines[i].trim()
            if (line.startsWith("//") && line.contains("自定义处理逻辑")) {
                logger.info("Found custom logic comment at line ${i + 1}: '$line'")
                return InsertPosition(i + 1, line) // 在注释后插入
            }
        }

        // 如果没找到注释，查找其他可能的锚点
        // 查找 "/** This block is generated by vs, do not modify, end anchor 1 */"
        for (i in origLines.indices) {
            val line = origLines[i].trim()
            if (line.contains("end anchor 1")) {
                logger.info("Found end anchor 1 at line ${i + 1}")
                // 在这行后面查找注释行
                for (j in (i + 1) until origLines.size) {
                    val nextLine = origLines[j].trim()
                    if (nextLine.startsWith("//") && nextLine.contains("自定义")) {
                        logger.info("Found custom comment after anchor 1 at line ${j + 1}: '$nextLine'")
                        return InsertPosition(j + 1, nextLine)
                    }
                    if (nextLine.contains("start anchor 2")) {
                        logger.info("Found start anchor 2 at line ${j + 1}, inserting before it")
                        // 在start anchor 2之前插入
                        return InsertPosition(j, "before anchor 2")
                    }
                }
            }
        }

        logger.warn("Could not find suitable insertion position")
        return null
    }

    /**
     * 重新构建patch，修正hunk header，然后使用标准apply流程
     */
    private fun rebuildAndApplyPatch(
        origText: String,
        originalPatch: String,
        patchInfo: PatchInfo,
        position: InsertPosition
    ): Pair<String?, String?> {
        try {
            val origLines = origText.lines()

            // 找到插入位置（0-based）
            val insertLineIndex = position.lineNumber - 1 // 转换为0-based

            // 我们需要一些上下文行来确保patch能正确应用
            // 通常取插入位置前面的几行作为上下文
            val contextBefore = 3 // 取前3行作为上下文
            val contextStart = maxOf(0, insertLineIndex - contextBefore)
            val contextEnd = minOf(origLines.size, insertLineIndex)

            // 计算实际的上下文行数
            val actualContextCount = contextEnd - contextStart
            val addedLinesCount = patchInfo.addedLines.size

            // 构建新的hunk header参数
            val newOldStart = contextStart + 1 // 转换为1-based
            val newOldCount = actualContextCount
            val newNewStart = newOldStart
            val newNewCount = actualContextCount + addedLinesCount

            logger.info("Rebuilding patch at position $insertLineIndex:")
            logger.info("  Context: lines $contextStart to $contextEnd (count: $actualContextCount)")
            logger.info("  Hunk: old=($newOldStart,$newOldCount) new=($newNewStart,$newNewCount)")

            // 构建新的patch内容
            val newPatchContent = buildRebuiltPatch(
                origLines,
                contextStart,
                contextEnd,
                patchInfo.addedLines,
                newOldStart,
                newOldCount,
                newNewStart,
                newNewCount
            )

            logger.info("Rebuilt patch content:\n$newPatchContent")

            // 使用标准流程应用重建的patch
            val gitDiffPath = "diff --git a/test b/test\n" +
                    "--- a/test\n" +
                    "+++ b/test\n" + newPatchContent
            val patchReader = PatchReader(gitDiffPath)
            val patches = patchReader.readTextPatches()
            val patch = patches.firstOrNull()

            if (patch != null) {
                val appliedPatch = GenericPatchApplier.apply(origText, patch.hunks)
                if (appliedPatch?.status == ApplyPatchStatus.SUCCESS) {
                    logger.info("Rebuilt patch applied successfully")
                    return Pair(appliedPatch.patchedText, null)
                } else {
                    logger.warn("Rebuilt patch application failed")
                    return Pair(null, "Rebuilt patch application failed")
                }
            } else {
                logger.warn("Failed to parse rebuilt patch")
                return Pair(null, "Failed to parse rebuilt patch")
            }

        } catch (e: Exception) {
            logger.warn("Error in rebuildAndApplyPatch: ${e.message}")
            return Pair(null, "Rebuild patch error: ${e.message}")
        }
    }

    /**
     * 构建重建后的patch内容
     */
    private fun buildRebuiltPatch(
        origLines: List<String>,
        contextStart: Int,
        contextEnd: Int,
        addedLines: List<String>,
        oldStart: Int,
        oldCount: Int,
        newStart: Int,
        newCount: Int
    ): String {
        val sb = StringBuilder()

        // 添加hunk header
        sb.append("@@ -$oldStart,$oldCount +$newStart,$newCount @@\n")

        // 添加上下文行（在插入点之前）
        for (i in contextStart until contextEnd) {
            if (i < origLines.size) {
                sb.append(" ${origLines[i]}\n")
            }
        }

        // 添加新增行
        for (addedLine in addedLines) {
            sb.append("+$addedLine\n")
        }

        return sb.toString()
    }

    /**
     * Patch信息数据类
     */
    private data class PatchInfo(
        val contextLines: List<String>,
        val addedLines: List<String>,
        val hasCustomComment: Boolean
    )

    /**
     * 插入位置数据类
     */
    private data class InsertPosition(
        val lineNumber: Int,
        val anchorLine: String
    )
}
