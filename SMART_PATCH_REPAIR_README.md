# 智能Patch修复功能

## 概述

为了解决代码合并过程中行号偏移导致的patch应用失败问题，我们在 `TextFilePatcher` 中实现了智能修复功能。该功能能够自动检测并修复由于行号不匹配导致的patch应用失败。

## 问题背景

在代码生成和合并过程中，经常会遇到以下问题：

```
PATCH_APPLICATION_ERROR: Line content mismatch at line 77. 
Expected content: '                convertToPharmacyInboundSaveResultVoMap(dtoList)', 
but patch line contains: '        )'. 
The file content has changed since the patch was created.
```

这种错误通常发生在：
1. Patch中的起始行号与实际文件不匹配
2. 文件在patch创建后发生了轻微修改
3. 代码生成工具计算行号时出现偏差

## 解决方案

### 1. 智能修复流程

当标准patch应用失败时，系统会自动启动智能修复流程：

```kotlin
// 尝试智能修复patch并重新应用
logger.info("Standard patch application failed, attempting smart patch repair...")
val smartRepairResult = attemptSmartPatchRepair(origText, processedPatch)
if (smartRepairResult.first != null) {
    logger.info("Smart patch repair succeeded")
    return smartRepairResult
}
```

### 2. 上下文感知的位置查找

系统通过查找特定的锚点来确定正确的插入位置，而不是依赖可能错误的行号：

#### 主要锚点策略：
1. **自定义处理逻辑注释**：`//自定义处理逻辑`
2. **代码生成标记**：`end anchor 1` 和 `start anchor 2`
3. **上下文匹配**：基于周围代码内容进行匹配

```kotlin
// 查找特定的锚点：注释行 "//自定义处理逻辑"
for (i in origLines.indices) {
    val line = origLines[i].trim()
    if (line.startsWith("//") && line.contains("自定义处理逻辑")) {
        logger.info("Found custom logic comment at line ${i + 1}: '$line'")
        return InsertPosition(i + 1, line) // 在注释后插入
    }
}
```

### 3. Hunk Header重建

**关键改进**：找到正确位置后，系统会重新构建patch的hunk header，然后使用标准的patch应用流程：

```kotlin
// 重新构建patch，修正hunk header，然后使用标准apply流程
return rebuildAndApplyPatch(origText, patchText, patchInfo, bestPosition)
```

#### 重建过程：
1. **计算新的上下文范围**：基于找到的插入位置，确定合适的上下文行
2. **生成正确的hunk header**：`@@ -oldStart,oldCount +newStart,newCount @@`
3. **构建新的patch内容**：包含正确的上下文行和新增行
4. **使用标准流程应用**：通过 `GenericPatchApplier.apply()` 应用重建的patch

```kotlin
// 构建新的hunk header参数
val newOldStart = contextStart + 1 // 转换为1-based
val newOldCount = actualContextCount
val newNewStart = newOldStart
val newNewCount = actualContextCount + addedLinesCount

// 构建新的patch内容
val newPatchContent = buildRebuiltPatch(
    origLines, 
    contextStart, 
    contextEnd,
    patchInfo.addedLines,
    newOldStart, 
    newOldCount, 
    newNewStart, 
    newNewCount
)
```

## 优势

### 1. **稳健性**
- 使用标准的patch应用流程，确保一致性
- 多重锚点策略，提高成功率
- 详细的日志记录，便于调试

### 2. **兼容性**
- 不影响正常的patch应用流程
- 只在标准方法失败时才启动智能修复
- 保持与现有代码的完全兼容

### 3. **可维护性**
- 清晰的代码结构和注释
- 模块化的设计，易于扩展
- 完整的错误处理和日志记录

## 使用场景

该功能特别适用于：

1. **代码生成工具**：自动生成的patch可能存在行号偏差
2. **增量更新**：在现有代码基础上添加新功能
3. **模板应用**：将代码模板应用到不同的文件中
4. **自动化重构**：批量修改代码时的patch应用

## 示例

### 原始问题的Patch：
```diff
@@ -78,7 +78,15 @@
         )
      );
    /** This block is generated by vs, do not modify, end anchor 1 */
    //自定义处理逻辑
+    // 设置自定义字段值
+    for (Map.Entry<DrugImportBaseDto, PharmacyInboundSaveResultVo> entry : voMap.entrySet()) {
+      PharmacyInboundSaveResultVo vo = entry.getValue();
+      // 设置默认值，实际使用时应根据具体业务逻辑设置
+      vo.setSuccess(true);
+      vo.setMessage("操作成功");
+    }
+
    /** This block is generated by vs, do not modify, start anchor 2 */
    return voMap;
    /** This block is generated by vs, do not modify, end anchor 2 */
```

### 智能修复后的Patch：
```diff
@@ -27,3 +27,11 @@
        /** This block is generated by vs, do not modify, end anchor 1 */
        //自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
+    // 设置自定义字段值
+    for (Map.Entry<DrugImportBaseDto, PharmacyInboundSaveResultVo> entry : voMap.entrySet()) {
+      PharmacyInboundSaveResultVo vo = entry.getValue();
+      // 设置默认值，实际使用时应根据具体业务逻辑设置
+      vo.setSuccess(true);
+      vo.setMessage("操作成功");
+    }
+
```

## 日志输出

智能修复过程会产生详细的日志：

```
INFO - Standard patch application failed, attempting smart patch repair...
INFO - Searching for insertion position in 32 lines
INFO - Found custom logic comment at line 28: '//自定义处理逻辑'
INFO - Found best insertion position: line 29, anchor: '//自定义处理逻辑'
INFO - Rebuilding patch at position 28:
INFO -   Context: lines 25 to 28 (count: 3)
INFO -   Hunk: old=(26,3) new=(26,8)
INFO - Rebuilt patch applied successfully
INFO - Smart patch repair succeeded
```

## 总结

通过重建hunk header并使用标准patch应用流程的方法，我们实现了一个既稳健又兼容的智能patch修复功能。这种方法不仅解决了行号偏移问题，还保持了与现有系统的完全兼容性。
